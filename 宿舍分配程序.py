#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宿舍分配程序
功能：匹配法学院人员名单到宿舍安排，并将未匹配人员随机分配到空余床位
"""

import pandas as pd
import random
import sys
from pathlib import Path

def read_csv_with_encoding(file_path, encodings=['utf-8', 'gbk', 'gb2312', 'utf-8-sig']):
    """尝试不同编码读取CSV文件"""
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用 {encoding} 编码读取文件: {file_path}")
            return df
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"使用 {encoding} 编码读取文件时出错: {e}")
            continue
    
    raise ValueError(f"无法读取文件 {file_path}，尝试了所有编码: {encodings}")

def load_data():
    """加载人员名单和宿舍安排数据"""
    print("正在加载数据...")
    
    # 读取法学院人员名单
    students_df = read_csv_with_encoding('法学院人员名单.csv')
    print(f"法学院人员名单加载完成，共 {len(students_df)} 人")
    
    # 读取宿舍安排
    dorm_df = read_csv_with_encoding('宿舍安排.csv')
    print(f"宿舍安排加载完成，共 {len(dorm_df)} 个床位")
    
    return students_df, dorm_df

def analyze_data(students_df, dorm_df):
    """分析数据结构"""
    print("\n=== 数据分析 ===")
    
    print("法学院人员名单列名:", list(students_df.columns))
    print("宿舍安排列名:", list(dorm_df.columns))
    
    print(f"\n学生性别分布:")
    print(students_df['性别'].value_counts())
    
    print(f"\n宿舍床位性别分布:")
    print(dorm_df['性别'].value_counts())
    
    # 检查已分配的床位 - 检查所有姓名列
    occupied_beds_condition = (
        (dorm_df['姓名'].notna() & (dorm_df['姓名'] != '')) |
        (dorm_df['姓名.1'].notna() & (dorm_df['姓名.1'] != ''))
    )
    occupied_beds = dorm_df[occupied_beds_condition]
    empty_beds = dorm_df[~occupied_beds_condition]
    
    print(f"\n已分配床位: {len(occupied_beds)} 个")
    print(f"空余床位: {len(empty_beds)} 个")
    
    return occupied_beds, empty_beds

def match_existing_students(students_df, dorm_df):
    """匹配已在宿舍安排中的学生"""
    print("\n=== 匹配现有学生 ===")

    # 检查所有可能的姓名列
    occupied_students = []

    # 检查 '姓名' 列
    if '姓名' in dorm_df.columns:
        names_col1 = dorm_df[dorm_df['姓名'].notna() & (dorm_df['姓名'] != '')]['姓名'].tolist()
        occupied_students.extend(names_col1)
        print(f"从'姓名'列找到已分配学生: {len(names_col1)} 人")

    # 检查 '姓名.1' 列
    if '姓名.1' in dorm_df.columns:
        names_col2 = dorm_df[dorm_df['姓名.1'].notna() & (dorm_df['姓名.1'] != '')]['姓名.1'].tolist()
        occupied_students.extend(names_col2)
        print(f"从'姓名.1'列找到已分配学生: {len(names_col2)} 人")

    # 去重
    occupied_students = list(set(occupied_students))
    print(f"总共已分配学生: {len(occupied_students)} 人")

    # 在人员名单中查找匹配的学生
    matched_students = students_df[students_df['姓名'].isin(occupied_students)]
    unmatched_students = students_df[~students_df['姓名'].isin(occupied_students)]

    print(f"在人员名单中找到匹配学生: {len(matched_students)} 人")
    print(f"未匹配学生: {len(unmatched_students)} 人")

    return matched_students, unmatched_students

def assign_remaining_students(unmatched_students, dorm_df):
    """将未匹配的学生随机分配到空余床位"""
    print("\n=== 分配剩余学生 ===")
    
    # 获取空余床位 - 检查所有姓名列都为空的床位
    empty_beds_condition = (
        (dorm_df['姓名'].isna() | (dorm_df['姓名'] == '')) &
        (dorm_df['姓名.1'].isna() | (dorm_df['姓名.1'] == ''))
    )
    empty_beds = dorm_df[empty_beds_condition].copy()
    
    # 按性别分组
    male_students = unmatched_students[unmatched_students['性别'] == '男'].copy()
    female_students = unmatched_students[unmatched_students['性别'] == '女'].copy()
    
    male_beds = empty_beds[empty_beds['性别'] == '男'].copy()
    female_beds = empty_beds[empty_beds['性别'] == '女'].copy()
    
    print(f"待分配男学生: {len(male_students)} 人")
    print(f"待分配女学生: {len(female_students)} 人")
    print(f"空余男床位: {len(male_beds)} 个")
    print(f"空余女床位: {len(female_beds)} 个")
    
    # 创建宿舍安排副本用于更新
    updated_dorm_df = dorm_df.copy()
    
    # 分配男学生
    if len(male_students) > 0 and len(male_beds) > 0:
        assign_count = min(len(male_students), len(male_beds))
        
        # 随机打乱学生和床位顺序
        male_students_shuffled = male_students.sample(n=len(male_students), random_state=42).reset_index(drop=True)
        male_beds_shuffled = male_beds.sample(n=len(male_beds), random_state=42).reset_index(drop=True)
        
        for i in range(assign_count):
            student = male_students_shuffled.iloc[i]
            bed_idx = male_beds_shuffled.iloc[i].name
            
            # 更新宿舍安排
            updated_dorm_df.loc[bed_idx, '姓名'] = student['姓名']
            updated_dorm_df.loc[bed_idx, '学号'] = student['学号']
            updated_dorm_df.loc[bed_idx, '专业'] = student['专业']
            updated_dorm_df.loc[bed_idx, '年级'] = student['年级']
            updated_dorm_df.loc[bed_idx, '民族'] = student['民族']
        
        print(f"成功分配男学生: {assign_count} 人")
        if len(male_students) > len(male_beds):
            print(f"警告: 还有 {len(male_students) - len(male_beds)} 名男学生未能分配床位")
    
    # 分配女学生
    if len(female_students) > 0 and len(female_beds) > 0:
        assign_count = min(len(female_students), len(female_beds))
        
        # 随机打乱学生和床位顺序
        female_students_shuffled = female_students.sample(n=len(female_students), random_state=42).reset_index(drop=True)
        female_beds_shuffled = female_beds.sample(n=len(female_beds), random_state=42).reset_index(drop=True)
        
        for i in range(assign_count):
            student = female_students_shuffled.iloc[i]
            bed_idx = female_beds_shuffled.iloc[i].name
            
            # 更新宿舍安排
            updated_dorm_df.loc[bed_idx, '姓名'] = student['姓名']
            updated_dorm_df.loc[bed_idx, '学号'] = student['学号']
            updated_dorm_df.loc[bed_idx, '专业'] = student['专业']
            updated_dorm_df.loc[bed_idx, '年级'] = student['年级']
            updated_dorm_df.loc[bed_idx, '民族'] = student['民族']
        
        print(f"成功分配女学生: {assign_count} 人")
        if len(female_students) > len(female_beds):
            print(f"警告: 还有 {len(female_students) - len(female_beds)} 名女学生未能分配床位")
    
    return updated_dorm_df

def save_results(updated_dorm_df, unmatched_students, matched_students):
    """保存结果"""
    print("\n=== 保存结果 ===")
    
    # 保存更新后的宿舍安排
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'宿舍安排_更新后_{timestamp}.csv'
    updated_dorm_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"更新后的宿舍安排已保存到: {output_file}")
    
    # 统计最终结果
    final_occupied = updated_dorm_df[updated_dorm_df['姓名'].notna() & (updated_dorm_df['姓名'] != '')]
    final_empty = updated_dorm_df[updated_dorm_df['姓名'].isna() | (updated_dorm_df['姓名'] == '')]
    
    print(f"\n=== 最终统计 ===")
    print(f"总床位数: {len(updated_dorm_df)}")
    print(f"已分配床位: {len(final_occupied)}")
    print(f"剩余空床位: {len(final_empty)}")
    
    # 检查是否还有未分配的学生
    assigned_students = final_occupied['姓名'].tolist()
    all_students = matched_students['姓名'].tolist() + unmatched_students['姓名'].tolist()
    still_unassigned = [name for name in all_students if name not in assigned_students]

    print(f"\n=== 分配详情 ===")
    assigned_male = final_occupied[final_occupied['性别'] == '男']
    assigned_female = final_occupied[final_occupied['性别'] == '女']
    print(f"已分配男学生: {len(assigned_male)} 人")
    print(f"已分配女学生: {len(assigned_female)} 人")

    if still_unassigned:
        print(f"仍未分配的学生: {len(still_unassigned)} 人")
        unassigned_df = pd.DataFrame({'未分配学生': still_unassigned})
        unassigned_df.to_csv('未分配学生名单.csv', index=False, encoding='utf-8-sig')
        print("未分配学生名单已保存到: 未分配学生名单.csv")
    else:
        print("所有学生都已成功分配宿舍!")

def main():
    """主函数"""
    print("=== 宿舍分配程序开始运行 ===")
    
    try:
        # 1. 加载数据
        students_df, dorm_df = load_data()
        
        # 2. 分析数据
        occupied_beds, empty_beds = analyze_data(students_df, dorm_df)
        
        # 3. 匹配现有学生
        matched_students, unmatched_students = match_existing_students(students_df, dorm_df)
        
        # 4. 分配剩余学生
        updated_dorm_df = assign_remaining_students(unmatched_students, dorm_df)
        
        # 5. 保存结果
        save_results(updated_dorm_df, unmatched_students, matched_students)
        
        print("\n=== 程序运行完成 ===")
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
