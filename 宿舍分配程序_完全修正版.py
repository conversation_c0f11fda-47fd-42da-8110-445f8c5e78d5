#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宿舍分配程序 - 完全修正版
功能：匹配法学院人员名单到宿舍安排，并将未匹配人员随机分配到空余床位中
修正：正确识别空置床位（以姓名为空为准），处理床位不足问题
"""

import pandas as pd
import random
import datetime
from collections import defaultdict

def read_csv_with_encoding(file_path, encodings=['utf-8', 'gbk', 'gb2312', 'utf-8-sig']):
    """尝试不同编码读取CSV文件"""
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用 {encoding} 编码读取文件: {file_path}")
            return df
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"使用 {encoding} 编码读取文件时出错: {e}")
            continue
    
    raise ValueError(f"无法读取文件 {file_path}，尝试了所有编码: {encodings}")

def load_data():
    """加载人员名单和宿舍安排数据"""
    print("正在加载数据...")
    
    # 读取法学院人员名单
    students_df = read_csv_with_encoding('法学院人员名单.csv')
    print(f"法学院人员名单加载完成，共 {len(students_df)} 人")
    
    # 读取宿舍安排
    dorm_df = read_csv_with_encoding('宿舍安排.csv')
    print(f"宿舍安排加载完成，共 {len(dorm_df)} 个床位")
    
    return students_df, dorm_df

def analyze_data_capacity(students_df, dorm_df):
    """分析数据和分配能力"""
    print("\n=== 数据容量分析 ===")
    
    # 学生统计
    male_students = len(students_df[students_df['性别'] == '男'])
    female_students = len(students_df[students_df['性别'] == '女'])
    print(f"学生总数: {len(students_df)} (男{male_students}人, 女{female_students}人)")
    
    # 床位统计
    male_beds = len(dorm_df[dorm_df['性别'] == '男'])
    female_beds = len(dorm_df[dorm_df['性别'] == '女'])
    print(f"床位总数: {len(dorm_df)} (男{male_beds}个, 女{female_beds}个)")
    
    # 空置床位统计（以姓名为空为准）
    empty_beds = dorm_df[dorm_df['姓名'].isna() | (dorm_df['姓名'] == '')]
    empty_male_beds = len(empty_beds[empty_beds['性别'] == '男'])
    empty_female_beds = len(empty_beds[empty_beds['性别'] == '女'])
    print(f"空置床位: {len(empty_beds)} (男{empty_male_beds}个, 女{empty_female_beds}个)")
    
    # 分配能力分析
    print(f"\n=== 分配能力分析 ===")
    male_deficit = male_students - empty_male_beds
    female_deficit = female_students - empty_female_beds
    
    print(f"男学生分配情况: {male_students}人 vs {empty_male_beds}个床位, 差额: {male_deficit}")
    print(f"女学生分配情况: {female_students}人 vs {empty_female_beds}个床位, 差额: {female_deficit}")
    
    if male_deficit > 0:
        print(f"⚠️  警告: 男床位不足 {male_deficit} 个")
    if female_deficit > 0:
        print(f"⚠️  警告: 女床位不足 {female_deficit} 个")
    
    return empty_male_beds, empty_female_beds, male_deficit, female_deficit

def analyze_floor_gender_distribution(dorm_df):
    """分析楼层性别分布"""
    print("\n=== 楼层性别分布分析 ===")
    
    # 提取楼层信息
    dorm_df['楼层'] = dorm_df['房间号'].astype(str).str[:-2]
    
    # 分析每个楼层的性别分布
    floor_gender = dorm_df.groupby(['宿舍楼', '楼层', '性别']).size().reset_index(name='床位数')
    print("楼层性别分布:")
    print(floor_gender)
    
    # 检查混住情况
    print("\n=== 混住情况检查 ===")
    mixed_floors = []
    for building in dorm_df['宿舍楼'].unique():
        building_data = dorm_df[dorm_df['宿舍楼'] == building]
        for floor in building_data['楼层'].unique():
            floor_data = building_data[building_data['楼层'] == floor]
            genders = floor_data['性别'].unique()
            if len(genders) > 1:
                mixed_floors.append(f'{building}-{floor}楼')
                print(f'混住楼层: {building} {floor}楼 - 性别: {genders}')
    
    if not mixed_floors:
        print('没有发现混住楼层')
    else:
        print(f'发现 {len(mixed_floors)} 个混住楼层')
    
    return mixed_floors

def get_floor_preference_order(dorm_df):
    """获取楼层性别偏好顺序，优先分配到同性别楼层"""
    dorm_df['楼层'] = dorm_df['房间号'].astype(str).str[:-2]
    
    # 统计每个楼层的性别分布
    floor_stats = defaultdict(lambda: {'男': 0, '女': 0})
    
    for _, row in dorm_df.iterrows():
        building = row['宿舍楼']
        floor = row['楼层']
        gender = row['性别']
        key = f"{building}-{floor}"
        floor_stats[key][gender] += 1
    
    # 分类楼层
    male_floors = []      # 男生楼层
    female_floors = []    # 女生楼层
    mixed_floors = []     # 混合楼层
    
    for floor_key, stats in floor_stats.items():
        if stats['男'] > 0 and stats['女'] == 0:
            male_floors.append(floor_key)
        elif stats['女'] > 0 and stats['男'] == 0:
            female_floors.append(floor_key)
        else:
            mixed_floors.append(floor_key)
    
    print(f"\n=== 楼层分类 ===")
    print(f"男生楼层: {len(male_floors)} 个")
    print(f"女生楼层: {len(female_floors)} 个") 
    print(f"混合楼层: {len(mixed_floors)} 个")
    
    return male_floors, female_floors, mixed_floors, floor_stats

def match_existing_students(students_df, dorm_df):
    """匹配已在宿舍安排中的学生"""
    print("\n=== 匹配现有学生 ===")
    
    # 获取宿舍安排中的学生姓名（从姓名列）
    occupied_students = []
    if '姓名' in dorm_df.columns:
        names_list = dorm_df[dorm_df['姓名'].notna() & (dorm_df['姓名'] != '')]['姓名'].tolist()
        occupied_students.extend(names_list)
        print(f"从宿舍安排中找到已分配学生: {len(names_list)} 人")
        if len(names_list) > 0:
            print(f"前5个已分配学生: {names_list[:5]}")
    
    # 去重
    occupied_students = list(set(occupied_students))
    print(f"总共已分配学生: {len(occupied_students)} 人")
    
    # 在人员名单中查找匹配的学生
    matched_students = students_df[students_df['姓名'].isin(occupied_students)]
    unmatched_students = students_df[~students_df['姓名'].isin(occupied_students)]
    
    print(f"在人员名单中找到匹配学生: {len(matched_students)} 人")
    print(f"未匹配学生: {len(unmatched_students)} 人")
    
    # 如果没有匹配的学生，说明原始宿舍安排中的姓名是示例数据
    if len(matched_students) == 0:
        print("注意: 原始宿舍安排中的学生姓名与法学院人员名单不匹配，将所有学生视为待分配")
        unmatched_students = students_df.copy()
    
    return matched_students, unmatched_students

def prepare_dorm_dataframe(dorm_df):
    """准备宿舍数据框，确保数据类型正确"""
    # 创建副本
    updated_dorm_df = dorm_df.copy()
    
    # 确保关键列的数据类型
    if '姓名' in updated_dorm_df.columns:
        updated_dorm_df['姓名'] = updated_dorm_df['姓名'].astype('object')
    if '学号' in updated_dorm_df.columns:
        updated_dorm_df['学号'] = updated_dorm_df['学号'].astype('object')
    if '专业' in updated_dorm_df.columns:
        updated_dorm_df['专业'] = updated_dorm_df['专业'].astype('object')
    if '年级' in updated_dorm_df.columns:
        updated_dorm_df['年级'] = updated_dorm_df['年级'].astype('object')
    if '民族' in updated_dorm_df.columns:
        updated_dorm_df['民族'] = updated_dorm_df['民族'].astype('object')
    
    return updated_dorm_df

def assign_students_with_floor_separation(unmatched_students, dorm_df, male_floors, female_floors, mixed_floors):
    """按楼层性别分离原则分配学生"""
    print("\n=== 按楼层分离原则分配学生 ===")
    
    # 准备宿舍数据框
    updated_dorm_df = prepare_dorm_dataframe(dorm_df)
    
    # 添加楼层信息
    updated_dorm_df['楼层'] = updated_dorm_df['房间号'].astype(str).str[:-2]
    updated_dorm_df['楼层标识'] = updated_dorm_df['宿舍楼'] + '-' + updated_dorm_df['楼层']
    
    # 获取空余床位（姓名为空的床位）
    empty_beds = updated_dorm_df[updated_dorm_df['姓名'].isna() | (updated_dorm_df['姓名'] == '')].copy()
    
    # 按性别分组
    male_students = unmatched_students[unmatched_students['性别'] == '男'].copy()
    female_students = unmatched_students[unmatched_students['性别'] == '女'].copy()
    
    # 分类空余床位
    male_empty_beds = empty_beds[empty_beds['性别'] == '男'].copy()
    female_empty_beds = empty_beds[empty_beds['性别'] == '女'].copy()
    
    print(f"待分配男学生: {len(male_students)} 人")
    print(f"待分配女学生: {len(female_students)} 人")
    print(f"空余男床位: {len(male_empty_beds)} 个")
    print(f"空余女床位: {len(female_empty_beds)} 个")
    
    # 分配男学生 - 优先分配到男生楼层
    if len(male_students) > 0 and len(male_empty_beds) > 0:
        assigned_count = assign_by_floor_preference(
            male_students, male_empty_beds, updated_dorm_df, 
            male_floors, mixed_floors, '男'
        )
        print(f"成功分配男学生: {assigned_count} 人")
        if len(male_students) > len(male_empty_beds):
            print(f"警告: 还有 {len(male_students) - len(male_empty_beds)} 名男学生未能分配床位")
    
    # 分配女学生 - 优先分配到女生楼层  
    if len(female_students) > 0 and len(female_empty_beds) > 0:
        assigned_count = assign_by_floor_preference(
            female_students, female_empty_beds, updated_dorm_df,
            female_floors, mixed_floors, '女'
        )
        print(f"成功分配女学生: {assigned_count} 人")
        if len(female_students) > len(female_empty_beds):
            print(f"警告: 还有 {len(female_students) - len(female_empty_beds)} 名女学生未能分配床位")
    
    return updated_dorm_df

def assign_by_floor_preference(students, empty_beds, updated_dorm_df, preferred_floors, mixed_floors, gender):
    """按楼层偏好分配学生"""
    assigned_count = 0
    max_assign = min(len(students), len(empty_beds))
    
    # 随机打乱学生顺序
    students_shuffled = students.sample(n=len(students), random_state=42).reset_index(drop=True)
    
    # 优先分配到同性别楼层
    preferred_beds = empty_beds[empty_beds['楼层标识'].isin(preferred_floors)].copy()
    mixed_beds = empty_beds[empty_beds['楼层标识'].isin(mixed_floors)].copy()
    
    # 先分配到偏好楼层
    if len(preferred_beds) > 0:
        preferred_beds_shuffled = preferred_beds.sample(n=len(preferred_beds), random_state=42).reset_index(drop=True)
        assign_count_preferred = min(len(students_shuffled), len(preferred_beds_shuffled))
        
        for i in range(assign_count_preferred):
            if assigned_count >= max_assign:
                break
            student = students_shuffled.iloc[i]
            bed_idx = preferred_beds_shuffled.iloc[i].name
            
            # 更新宿舍安排 - 使用安全的数据类型转换
            updated_dorm_df.at[bed_idx, '姓名'] = str(student['姓名'])
            updated_dorm_df.at[bed_idx, '学号'] = str(int(student['学号'])) if pd.notna(student['学号']) else ''
            updated_dorm_df.at[bed_idx, '专业'] = str(student['专业']) if pd.notna(student['专业']) else ''
            updated_dorm_df.at[bed_idx, '年级'] = str(int(student['年级'])) if pd.notna(student['年级']) else ''
            updated_dorm_df.at[bed_idx, '民族'] = str(student['民族']) if pd.notna(student['民族']) else ''
            updated_dorm_df.at[bed_idx, '使用情况'] = '已分配'
            
            assigned_count += 1
    
    # 如果还有学生未分配，分配到混合楼层
    if assigned_count < max_assign and len(mixed_beds) > 0:
        remaining_students = students_shuffled.iloc[assigned_count:]
        mixed_beds_shuffled = mixed_beds.sample(n=len(mixed_beds), random_state=42).reset_index(drop=True)
        assign_count_mixed = min(len(remaining_students), len(mixed_beds_shuffled))
        
        for i in range(assign_count_mixed):
            if assigned_count >= max_assign:
                break
            student = remaining_students.iloc[i]
            bed_idx = mixed_beds_shuffled.iloc[i].name
            
            # 更新宿舍安排 - 使用安全的数据类型转换
            updated_dorm_df.at[bed_idx, '姓名'] = str(student['姓名'])
            updated_dorm_df.at[bed_idx, '学号'] = str(int(student['学号'])) if pd.notna(student['学号']) else ''
            updated_dorm_df.at[bed_idx, '专业'] = str(student['专业']) if pd.notna(student['专业']) else ''
            updated_dorm_df.at[bed_idx, '年级'] = str(int(student['年级'])) if pd.notna(student['年级']) else ''
            updated_dorm_df.at[bed_idx, '民族'] = str(student['民族']) if pd.notna(student['民族']) else ''
            updated_dorm_df.at[bed_idx, '使用情况'] = '已分配'
            
            assigned_count += 1
    
    return assigned_count

def save_results(updated_dorm_df, unmatched_students, matched_students):
    """保存结果"""
    print("\n=== 保存结果 ===")
    
    # 保存更新后的宿舍安排
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'宿舍安排_完全修正版_{timestamp}.csv'
    updated_dorm_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"更新后的宿舍安排已保存到: {output_file}")
    
    # 重新分析楼层性别分布
    print("\n=== 分配后楼层性别分布检查 ===")
    mixed_floors_after = analyze_floor_gender_distribution(updated_dorm_df)
    
    # 统计最终结果
    final_assigned = updated_dorm_df[updated_dorm_df['使用情况'] == '已分配']
    final_empty = updated_dorm_df[updated_dorm_df['姓名'].isna() | (updated_dorm_df['姓名'] == '')]
    
    print(f"\n=== 最终统计 ===")
    print(f"总床位数: {len(updated_dorm_df)}")
    print(f"已分配床位: {len(final_assigned)}")
    print(f"剩余空床位: {len(final_empty)}")
    
    # 检查分配的学生信息完整性
    assigned_with_info = final_assigned[final_assigned['姓名'].notna() & (final_assigned['姓名'] != '')]
    print(f"有完整信息的已分配学生: {len(assigned_with_info)}")
    
    # 按性别统计分配情况
    if len(assigned_with_info) > 0:
        print(f"\n按性别统计已分配学生:")
        print(assigned_with_info['性别'].value_counts())
    
    # 检查是否还有未分配的学生
    assigned_student_names = assigned_with_info['姓名'].tolist()
    all_student_names = unmatched_students['姓名'].tolist()
    still_unassigned = [name for name in all_student_names if name not in assigned_student_names]
    
    if still_unassigned:
        print(f"仍未分配的学生: {len(still_unassigned)} 人")
        unassigned_df = pd.DataFrame({'未分配学生': still_unassigned})
        unassigned_df.to_csv(f'未分配学生名单_完全修正版_{timestamp}.csv', index=False, encoding='utf-8-sig')
        print(f"未分配学生名单已保存到: 未分配学生名单_完全修正版_{timestamp}.csv")
        
        # 按性别统计未分配学生
        unassigned_students_info = unmatched_students[unmatched_students['姓名'].isin(still_unassigned)]
        if len(unassigned_students_info) > 0:
            print(f"未分配学生性别分布:")
            print(unassigned_students_info['性别'].value_counts())
    else:
        print("所有学生都已成功分配宿舍!")
    
    return output_file

def main():
    """主函数"""
    print("=== 宿舍分配程序（完全修正版）开始运行 ===")
    
    try:
        # 1. 加载数据
        students_df, dorm_df = load_data()
        
        # 2. 分析数据容量和分配能力
        empty_male_beds, empty_female_beds, male_deficit, female_deficit = analyze_data_capacity(students_df, dorm_df)
        
        # 3. 分析楼层性别分布
        mixed_floors_before = analyze_floor_gender_distribution(dorm_df)
        
        # 4. 获取楼层偏好顺序
        male_floors, female_floors, mixed_floors, floor_stats = get_floor_preference_order(dorm_df)
        
        # 5. 匹配现有学生
        matched_students, unmatched_students = match_existing_students(students_df, dorm_df)
        
        # 6. 按楼层分离原则分配剩余学生
        updated_dorm_df = assign_students_with_floor_separation(
            unmatched_students, dorm_df, male_floors, female_floors, mixed_floors
        )
        
        # 7. 保存结果
        output_file = save_results(updated_dorm_df, unmatched_students, matched_students)
        
        print(f"\n=== 程序运行完成 ===")
        print(f"结果文件: {output_file}")
        
        # 8. 最终提醒
        if male_deficit > 0 or female_deficit > 0:
            print(f"\n⚠️  重要提醒:")
            if male_deficit > 0:
                print(f"   - 男床位不足 {male_deficit} 个，有 {male_deficit} 名男学生无法分配")
            if female_deficit > 0:
                print(f"   - 女床位不足 {female_deficit} 个，有 {female_deficit} 名女学生无法分配")
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
